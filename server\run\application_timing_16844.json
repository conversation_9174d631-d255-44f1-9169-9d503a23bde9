[{"name": "Process Start", "start": 1748240660451, "end": 1748240666095, "duration": 5644, "pid": 16844, "index": 0}, {"name": "Application Start", "start": 1748240666098, "end": 1748240669232, "duration": 3134, "pid": 16844, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748240666171, "end": 1748240666218, "duration": 47, "pid": 16844, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748240666219, "end": 1748240666294, "duration": 75, "pid": 16844, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748240666220, "end": 1748240666221, "duration": 1, "pid": 16844, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748240666224, "end": 1748240666225, "duration": 1, "pid": 16844, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748240666227, "end": 1748240666228, "duration": 1, "pid": 16844, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748240666229, "end": 1748240666230, "duration": 1, "pid": 16844, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748240666236, "end": 1748240666237, "duration": 1, "pid": 16844, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748240666239, "end": 1748240666239, "duration": 0, "pid": 16844, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748240666240, "end": 1748240666241, "duration": 1, "pid": 16844, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748240666242, "end": 1748240666243, "duration": 1, "pid": 16844, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748240666243, "end": 1748240666244, "duration": 1, "pid": 16844, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748240666245, "end": 1748240666246, "duration": 1, "pid": 16844, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748240666247, "end": 1748240666248, "duration": 1, "pid": 16844, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748240666249, "end": 1748240666250, "duration": 1, "pid": 16844, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748240666250, "end": 1748240666251, "duration": 1, "pid": 16844, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748240666252, "end": 1748240666253, "duration": 1, "pid": 16844, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748240666255, "end": 1748240666256, "duration": 1, "pid": 16844, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748240666257, "end": 1748240666257, "duration": 0, "pid": 16844, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748240666258, "end": 1748240666260, "duration": 2, "pid": 16844, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748240666261, "end": 1748240666262, "duration": 1, "pid": 16844, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748240666263, "end": 1748240666264, "duration": 1, "pid": 16844, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748240666265, "end": 1748240666266, "duration": 1, "pid": 16844, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748240666267, "end": 1748240666268, "duration": 1, "pid": 16844, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748240666273, "end": 1748240666273, "duration": 0, "pid": 16844, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748240666275, "end": 1748240666276, "duration": 1, "pid": 16844, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748240666279, "end": 1748240666281, "duration": 2, "pid": 16844, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748240666287, "end": 1748240666288, "duration": 1, "pid": 16844, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748240666293, "end": 1748240666293, "duration": 0, "pid": 16844, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748240666294, "end": 1748240666294, "duration": 0, "pid": 16844, "index": 30}, {"name": "Load extend/application.js", "start": 1748240666295, "end": 1748240666494, "duration": 199, "pid": 16844, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748240666297, "end": 1748240666298, "duration": 1, "pid": 16844, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748240666299, "end": 1748240666304, "duration": 5, "pid": 16844, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748240666305, "end": 1748240666314, "duration": 9, "pid": 16844, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748240666317, "end": 1748240666327, "duration": 10, "pid": 16844, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748240666329, "end": 1748240666334, "duration": 5, "pid": 16844, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748240666335, "end": 1748240666339, "duration": 4, "pid": 16844, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748240666340, "end": 1748240666483, "duration": 143, "pid": 16844, "index": 38}, {"name": "Load extend/request.js", "start": 1748240666494, "end": 1748240666516, "duration": 22, "pid": 16844, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748240666505, "end": 1748240666507, "duration": 2, "pid": 16844, "index": 40}, {"name": "Load extend/response.js", "start": 1748240666516, "end": 1748240666536, "duration": 20, "pid": 16844, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748240666524, "end": 1748240666528, "duration": 4, "pid": 16844, "index": 42}, {"name": "Load extend/context.js", "start": 1748240666536, "end": 1748240666705, "duration": 169, "pid": 16844, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748240666538, "end": 1748240666588, "duration": 50, "pid": 16844, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748240666588, "end": 1748240666591, "duration": 3, "pid": 16844, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748240666593, "end": 1748240666593, "duration": 0, "pid": 16844, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748240666595, "end": 1748240666677, "duration": 82, "pid": 16844, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748240666680, "end": 1748240666684, "duration": 4, "pid": 16844, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748240666688, "end": 1748240666689, "duration": 1, "pid": 16844, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748240666691, "end": 1748240666695, "duration": 4, "pid": 16844, "index": 50}, {"name": "Load extend/helper.js", "start": 1748240666705, "end": 1748240666813, "duration": 108, "pid": 16844, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748240666707, "end": 1748240666795, "duration": 88, "pid": 16844, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748240666801, "end": 1748240666802, "duration": 1, "pid": 16844, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748240666802, "end": 1748240666804, "duration": 2, "pid": 16844, "index": 54}, {"name": "Load app.js", "start": 1748240666814, "end": 1748240667000, "duration": 186, "pid": 16844, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748240666815, "end": 1748240666816, "duration": 1, "pid": 16844, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748240666817, "end": 1748240666822, "duration": 5, "pid": 16844, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748240666823, "end": 1748240666866, "duration": 43, "pid": 16844, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748240666867, "end": 1748240666902, "duration": 35, "pid": 16844, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748240666903, "end": 1748240666928, "duration": 25, "pid": 16844, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748240666929, "end": 1748240666931, "duration": 2, "pid": 16844, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748240666933, "end": 1748240666937, "duration": 4, "pid": 16844, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748240666938, "end": 1748240666939, "duration": 1, "pid": 16844, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748240666940, "end": 1748240666940, "duration": 0, "pid": 16844, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748240666941, "end": 1748240666942, "duration": 1, "pid": 16844, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748240666944, "end": 1748240666945, "duration": 1, "pid": 16844, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748240666945, "end": 1748240666946, "duration": 1, "pid": 16844, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748240666948, "end": 1748240666949, "duration": 1, "pid": 16844, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748240666951, "end": 1748240666956, "duration": 5, "pid": 16844, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748240666957, "end": 1748240666989, "duration": 32, "pid": 16844, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748240666990, "end": 1748240666997, "duration": 7, "pid": 16844, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748240667022, "end": 1748240669207, "duration": 2185, "pid": 16844, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748240667993, "end": 1748240668102, "duration": 109, "pid": 16844, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748240668024, "end": 1748240669139, "duration": 1115, "pid": 16844, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748240668143, "end": 1748240669231, "duration": 1088, "pid": 16844, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748240668276, "end": 1748240669215, "duration": 939, "pid": 16844, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748240668434, "end": 1748240669169, "duration": 735, "pid": 16844, "index": 77}, {"name": "Load Service", "start": 1748240668435, "end": 1748240668633, "duration": 198, "pid": 16844, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748240668435, "end": 1748240668633, "duration": 198, "pid": 16844, "index": 79}, {"name": "Load Middleware", "start": 1748240668633, "end": 1748240668912, "duration": 279, "pid": 16844, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748240668633, "end": 1748240668886, "duration": 253, "pid": 16844, "index": 81}, {"name": "Load Controller", "start": 1748240668912, "end": 1748240668997, "duration": 85, "pid": 16844, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748240668912, "end": 1748240668997, "duration": 85, "pid": 16844, "index": 83}, {"name": "Load Router", "start": 1748240668997, "end": 1748240669007, "duration": 10, "pid": 16844, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748240668998, "end": 1748240669000, "duration": 2, "pid": 16844, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748240669001, "end": 1748240669139, "duration": 138, "pid": 16844, "index": 86}]