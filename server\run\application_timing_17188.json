[{"name": "Process Start", "start": 1748240370895, "end": 1748240377150, "duration": 6255, "pid": 17188, "index": 0}, {"name": "Application Start", "start": 1748240377154, "end": 1748240380285, "duration": 3131, "pid": 17188, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748240377189, "end": 1748240377240, "duration": 51, "pid": 17188, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748240377240, "end": 1748240377298, "duration": 58, "pid": 17188, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748240377241, "end": 1748240377242, "duration": 1, "pid": 17188, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748240377244, "end": 1748240377245, "duration": 1, "pid": 17188, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748240377246, "end": 1748240377246, "duration": 0, "pid": 17188, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748240377247, "end": 1748240377248, "duration": 1, "pid": 17188, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748240377251, "end": 1748240377252, "duration": 1, "pid": 17188, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748240377253, "end": 1748240377253, "duration": 0, "pid": 17188, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748240377254, "end": 1748240377255, "duration": 1, "pid": 17188, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748240377257, "end": 1748240377257, "duration": 0, "pid": 17188, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748240377258, "end": 1748240377259, "duration": 1, "pid": 17188, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748240377260, "end": 1748240377261, "duration": 1, "pid": 17188, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748240377263, "end": 1748240377264, "duration": 1, "pid": 17188, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748240377265, "end": 1748240377266, "duration": 1, "pid": 17188, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748240377268, "end": 1748240377268, "duration": 0, "pid": 17188, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748240377270, "end": 1748240377271, "duration": 1, "pid": 17188, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748240377272, "end": 1748240377272, "duration": 0, "pid": 17188, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748240377273, "end": 1748240377274, "duration": 1, "pid": 17188, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748240377274, "end": 1748240377275, "duration": 1, "pid": 17188, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748240377276, "end": 1748240377276, "duration": 0, "pid": 17188, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748240377277, "end": 1748240377277, "duration": 0, "pid": 17188, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748240377278, "end": 1748240377279, "duration": 1, "pid": 17188, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748240377279, "end": 1748240377280, "duration": 1, "pid": 17188, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748240377282, "end": 1748240377282, "duration": 0, "pid": 17188, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748240377285, "end": 1748240377286, "duration": 1, "pid": 17188, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748240377289, "end": 1748240377289, "duration": 0, "pid": 17188, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748240377293, "end": 1748240377294, "duration": 1, "pid": 17188, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748240377297, "end": 1748240377297, "duration": 0, "pid": 17188, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748240377297, "end": 1748240377297, "duration": 0, "pid": 17188, "index": 30}, {"name": "Load extend/application.js", "start": 1748240377299, "end": 1748240377453, "duration": 154, "pid": 17188, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748240377300, "end": 1748240377301, "duration": 1, "pid": 17188, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748240377302, "end": 1748240377306, "duration": 4, "pid": 17188, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748240377307, "end": 1748240377314, "duration": 7, "pid": 17188, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748240377317, "end": 1748240377328, "duration": 11, "pid": 17188, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748240377332, "end": 1748240377339, "duration": 7, "pid": 17188, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748240377340, "end": 1748240377344, "duration": 4, "pid": 17188, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748240377345, "end": 1748240377441, "duration": 96, "pid": 17188, "index": 38}, {"name": "Load extend/request.js", "start": 1748240377453, "end": 1748240377475, "duration": 22, "pid": 17188, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748240377461, "end": 1748240377464, "duration": 3, "pid": 17188, "index": 40}, {"name": "Load extend/response.js", "start": 1748240377475, "end": 1748240377495, "duration": 20, "pid": 17188, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748240377484, "end": 1748240377487, "duration": 3, "pid": 17188, "index": 42}, {"name": "Load extend/context.js", "start": 1748240377496, "end": 1748240377600, "duration": 104, "pid": 17188, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748240377497, "end": 1748240377522, "duration": 25, "pid": 17188, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748240377523, "end": 1748240377526, "duration": 3, "pid": 17188, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748240377528, "end": 1748240377529, "duration": 1, "pid": 17188, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748240377531, "end": 1748240377574, "duration": 43, "pid": 17188, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748240377575, "end": 1748240377577, "duration": 2, "pid": 17188, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748240377579, "end": 1748240377580, "duration": 1, "pid": 17188, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748240377581, "end": 1748240377588, "duration": 7, "pid": 17188, "index": 50}, {"name": "Load extend/helper.js", "start": 1748240377600, "end": 1748240377661, "duration": 61, "pid": 17188, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748240377602, "end": 1748240377637, "duration": 35, "pid": 17188, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748240377645, "end": 1748240377646, "duration": 1, "pid": 17188, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748240377647, "end": 1748240377648, "duration": 1, "pid": 17188, "index": 54}, {"name": "Load app.js", "start": 1748240377661, "end": 1748240377810, "duration": 149, "pid": 17188, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748240377662, "end": 1748240377664, "duration": 2, "pid": 17188, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748240377665, "end": 1748240377672, "duration": 7, "pid": 17188, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748240377674, "end": 1748240377696, "duration": 22, "pid": 17188, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748240377697, "end": 1748240377723, "duration": 26, "pid": 17188, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748240377723, "end": 1748240377745, "duration": 22, "pid": 17188, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748240377746, "end": 1748240377747, "duration": 1, "pid": 17188, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748240377747, "end": 1748240377751, "duration": 4, "pid": 17188, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748240377752, "end": 1748240377752, "duration": 0, "pid": 17188, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748240377753, "end": 1748240377754, "duration": 1, "pid": 17188, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748240377754, "end": 1748240377755, "duration": 1, "pid": 17188, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748240377756, "end": 1748240377757, "duration": 1, "pid": 17188, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748240377758, "end": 1748240377758, "duration": 0, "pid": 17188, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748240377759, "end": 1748240377759, "duration": 0, "pid": 17188, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748240377760, "end": 1748240377763, "duration": 3, "pid": 17188, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748240377764, "end": 1748240377791, "duration": 27, "pid": 17188, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748240377792, "end": 1748240377808, "duration": 16, "pid": 17188, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748240377831, "end": 1748240380255, "duration": 2424, "pid": 17188, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748240379091, "end": 1748240379191, "duration": 100, "pid": 17188, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748240379121, "end": 1748240380185, "duration": 1064, "pid": 17188, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748240379227, "end": 1748240380282, "duration": 1055, "pid": 17188, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748240379345, "end": 1748240380263, "duration": 918, "pid": 17188, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748240379504, "end": 1748240380213, "duration": 709, "pid": 17188, "index": 77}, {"name": "Load Service", "start": 1748240379504, "end": 1748240379715, "duration": 211, "pid": 17188, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748240379505, "end": 1748240379715, "duration": 210, "pid": 17188, "index": 79}, {"name": "Load Middleware", "start": 1748240379715, "end": 1748240379998, "duration": 283, "pid": 17188, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748240379716, "end": 1748240379973, "duration": 257, "pid": 17188, "index": 81}, {"name": "Load Controller", "start": 1748240379998, "end": 1748240380076, "duration": 78, "pid": 17188, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748240379998, "end": 1748240380076, "duration": 78, "pid": 17188, "index": 83}, {"name": "Load Router", "start": 1748240380076, "end": 1748240380088, "duration": 12, "pid": 17188, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748240380077, "end": 1748240380078, "duration": 1, "pid": 17188, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748240380080, "end": 1748240380185, "duration": 105, "pid": 17188, "index": 86}]