[{"name": "Process Start", "start": 1748240471309, "end": 1748240476677, "duration": 5368, "pid": 32528, "index": 0}, {"name": "Application Start", "start": 1748240476679, "end": 1748240480608, "duration": 3929, "pid": 32528, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748240476761, "end": 1748240476841, "duration": 80, "pid": 32528, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748240476841, "end": 1748240476961, "duration": 120, "pid": 32528, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748240476843, "end": 1748240476844, "duration": 1, "pid": 32528, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748240476848, "end": 1748240476850, "duration": 2, "pid": 32528, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748240476851, "end": 1748240476852, "duration": 1, "pid": 32528, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748240476853, "end": 1748240476854, "duration": 1, "pid": 32528, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748240476856, "end": 1748240476857, "duration": 1, "pid": 32528, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748240476860, "end": 1748240476861, "duration": 1, "pid": 32528, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748240476862, "end": 1748240476863, "duration": 1, "pid": 32528, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748240476864, "end": 1748240476865, "duration": 1, "pid": 32528, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748240476867, "end": 1748240476869, "duration": 2, "pid": 32528, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748240476870, "end": 1748240476872, "duration": 2, "pid": 32528, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748240476874, "end": 1748240476876, "duration": 2, "pid": 32528, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748240476877, "end": 1748240476878, "duration": 1, "pid": 32528, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748240476880, "end": 1748240476881, "duration": 1, "pid": 32528, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748240476885, "end": 1748240476887, "duration": 2, "pid": 32528, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748240476889, "end": 1748240476891, "duration": 2, "pid": 32528, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748240476893, "end": 1748240476895, "duration": 2, "pid": 32528, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748240476896, "end": 1748240476896, "duration": 0, "pid": 32528, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748240476897, "end": 1748240476898, "duration": 1, "pid": 32528, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748240476900, "end": 1748240476902, "duration": 2, "pid": 32528, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748240476904, "end": 1748240476905, "duration": 1, "pid": 32528, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748240476907, "end": 1748240476909, "duration": 2, "pid": 32528, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748240476913, "end": 1748240476913, "duration": 0, "pid": 32528, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748240476918, "end": 1748240476919, "duration": 1, "pid": 32528, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748240476925, "end": 1748240476928, "duration": 3, "pid": 32528, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748240476948, "end": 1748240476949, "duration": 1, "pid": 32528, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748240476957, "end": 1748240476959, "duration": 2, "pid": 32528, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748240476960, "end": 1748240476960, "duration": 0, "pid": 32528, "index": 30}, {"name": "Load extend/application.js", "start": 1748240476963, "end": 1748240477394, "duration": 431, "pid": 32528, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748240476969, "end": 1748240476972, "duration": 3, "pid": 32528, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748240476973, "end": 1748240476978, "duration": 5, "pid": 32528, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748240476980, "end": 1748240477026, "duration": 46, "pid": 32528, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748240477030, "end": 1748240477068, "duration": 38, "pid": 32528, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748240477072, "end": 1748240477084, "duration": 12, "pid": 32528, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748240477090, "end": 1748240477106, "duration": 16, "pid": 32528, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748240477108, "end": 1748240477378, "duration": 270, "pid": 32528, "index": 38}, {"name": "Load extend/request.js", "start": 1748240477394, "end": 1748240477421, "duration": 27, "pid": 32528, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748240477405, "end": 1748240477410, "duration": 5, "pid": 32528, "index": 40}, {"name": "Load extend/response.js", "start": 1748240477421, "end": 1748240477448, "duration": 27, "pid": 32528, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748240477432, "end": 1748240477439, "duration": 7, "pid": 32528, "index": 42}, {"name": "Load extend/context.js", "start": 1748240477448, "end": 1748240477601, "duration": 153, "pid": 32528, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748240477450, "end": 1748240477476, "duration": 26, "pid": 32528, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748240477477, "end": 1748240477480, "duration": 3, "pid": 32528, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748240477482, "end": 1748240477483, "duration": 1, "pid": 32528, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748240477488, "end": 1748240477582, "duration": 94, "pid": 32528, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748240477584, "end": 1748240477586, "duration": 2, "pid": 32528, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748240477587, "end": 1748240477588, "duration": 1, "pid": 32528, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748240477589, "end": 1748240477593, "duration": 4, "pid": 32528, "index": 50}, {"name": "Load extend/helper.js", "start": 1748240477601, "end": 1748240477666, "duration": 65, "pid": 32528, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748240477602, "end": 1748240477650, "duration": 48, "pid": 32528, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748240477655, "end": 1748240477656, "duration": 1, "pid": 32528, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748240477657, "end": 1748240477658, "duration": 1, "pid": 32528, "index": 54}, {"name": "Load app.js", "start": 1748240477666, "end": 1748240477784, "duration": 118, "pid": 32528, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748240477667, "end": 1748240477667, "duration": 0, "pid": 32528, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748240477668, "end": 1748240477672, "duration": 4, "pid": 32528, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748240477673, "end": 1748240477694, "duration": 21, "pid": 32528, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748240477695, "end": 1748240477713, "duration": 18, "pid": 32528, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748240477713, "end": 1748240477729, "duration": 16, "pid": 32528, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748240477730, "end": 1748240477731, "duration": 1, "pid": 32528, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748240477731, "end": 1748240477734, "duration": 3, "pid": 32528, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748240477735, "end": 1748240477735, "duration": 0, "pid": 32528, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748240477736, "end": 1748240477736, "duration": 0, "pid": 32528, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748240477737, "end": 1748240477737, "duration": 0, "pid": 32528, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748240477738, "end": 1748240477738, "duration": 0, "pid": 32528, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748240477739, "end": 1748240477739, "duration": 0, "pid": 32528, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748240477740, "end": 1748240477741, "duration": 1, "pid": 32528, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748240477742, "end": 1748240477745, "duration": 3, "pid": 32528, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748240477746, "end": 1748240477774, "duration": 28, "pid": 32528, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748240477774, "end": 1748240477781, "duration": 7, "pid": 32528, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748240477797, "end": 1748240480584, "duration": 2787, "pid": 32528, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748240478850, "end": 1748240479015, "duration": 165, "pid": 32528, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748240478886, "end": 1748240480499, "duration": 1613, "pid": 32528, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748240479063, "end": 1748240480607, "duration": 1544, "pid": 32528, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748240479521, "end": 1748240480588, "duration": 1067, "pid": 32528, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748240479796, "end": 1748240480530, "duration": 734, "pid": 32528, "index": 77}, {"name": "Load Service", "start": 1748240479797, "end": 1748240480057, "duration": 260, "pid": 32528, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748240479797, "end": 1748240480057, "duration": 260, "pid": 32528, "index": 79}, {"name": "Load Middleware", "start": 1748240480058, "end": 1748240480301, "duration": 243, "pid": 32528, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748240480058, "end": 1748240480277, "duration": 219, "pid": 32528, "index": 81}, {"name": "Load Controller", "start": 1748240480301, "end": 1748240480367, "duration": 66, "pid": 32528, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748240480301, "end": 1748240480367, "duration": 66, "pid": 32528, "index": 83}, {"name": "Load Router", "start": 1748240480367, "end": 1748240480375, "duration": 8, "pid": 32528, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748240480367, "end": 1748240480368, "duration": 1, "pid": 32528, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748240480369, "end": 1748240480498, "duration": 129, "pid": 32528, "index": 86}]