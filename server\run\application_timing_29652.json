[{"name": "Process Start", "start": 1748240565896, "end": 1748240568261, "duration": 2365, "pid": 29652, "index": 0}, {"name": "Application Start", "start": 1748240568263, "end": 1748240570582, "duration": 2319, "pid": 29652, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748240568285, "end": 1748240568324, "duration": 39, "pid": 29652, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748240568324, "end": 1748240568375, "duration": 51, "pid": 29652, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748240568325, "end": 1748240568325, "duration": 0, "pid": 29652, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748240568328, "end": 1748240568328, "duration": 0, "pid": 29652, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748240568329, "end": 1748240568329, "duration": 0, "pid": 29652, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748240568330, "end": 1748240568331, "duration": 1, "pid": 29652, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748240568332, "end": 1748240568333, "duration": 1, "pid": 29652, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748240568334, "end": 1748240568334, "duration": 0, "pid": 29652, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748240568335, "end": 1748240568335, "duration": 0, "pid": 29652, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748240568336, "end": 1748240568336, "duration": 0, "pid": 29652, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748240568337, "end": 1748240568337, "duration": 0, "pid": 29652, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748240568338, "end": 1748240568339, "duration": 1, "pid": 29652, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748240568340, "end": 1748240568341, "duration": 1, "pid": 29652, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748240568342, "end": 1748240568343, "duration": 1, "pid": 29652, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748240568343, "end": 1748240568344, "duration": 1, "pid": 29652, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748240568345, "end": 1748240568345, "duration": 0, "pid": 29652, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748240568346, "end": 1748240568347, "duration": 1, "pid": 29652, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748240568347, "end": 1748240568348, "duration": 1, "pid": 29652, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748240568349, "end": 1748240568349, "duration": 0, "pid": 29652, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748240568350, "end": 1748240568351, "duration": 1, "pid": 29652, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748240568352, "end": 1748240568352, "duration": 0, "pid": 29652, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748240568353, "end": 1748240568354, "duration": 1, "pid": 29652, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748240568355, "end": 1748240568357, "duration": 2, "pid": 29652, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748240568359, "end": 1748240568359, "duration": 0, "pid": 29652, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748240568362, "end": 1748240568362, "duration": 0, "pid": 29652, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748240568365, "end": 1748240568365, "duration": 0, "pid": 29652, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748240568368, "end": 1748240568368, "duration": 0, "pid": 29652, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748240568374, "end": 1748240568374, "duration": 0, "pid": 29652, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748240568375, "end": 1748240568375, "duration": 0, "pid": 29652, "index": 30}, {"name": "Load extend/application.js", "start": 1748240568376, "end": 1748240568487, "duration": 111, "pid": 29652, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748240568377, "end": 1748240568378, "duration": 1, "pid": 29652, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748240568378, "end": 1748240568380, "duration": 2, "pid": 29652, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748240568381, "end": 1748240568388, "duration": 7, "pid": 29652, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748240568389, "end": 1748240568396, "duration": 7, "pid": 29652, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748240568397, "end": 1748240568400, "duration": 3, "pid": 29652, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748240568401, "end": 1748240568404, "duration": 3, "pid": 29652, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748240568405, "end": 1748240568477, "duration": 72, "pid": 29652, "index": 38}, {"name": "Load extend/request.js", "start": 1748240568488, "end": 1748240568506, "duration": 18, "pid": 29652, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748240568496, "end": 1748240568499, "duration": 3, "pid": 29652, "index": 40}, {"name": "Load extend/response.js", "start": 1748240568506, "end": 1748240568535, "duration": 29, "pid": 29652, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748240568518, "end": 1748240568523, "duration": 5, "pid": 29652, "index": 42}, {"name": "Load extend/context.js", "start": 1748240568535, "end": 1748240568625, "duration": 90, "pid": 29652, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748240568536, "end": 1748240568564, "duration": 28, "pid": 29652, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748240568565, "end": 1748240568568, "duration": 3, "pid": 29652, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748240568569, "end": 1748240568570, "duration": 1, "pid": 29652, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748240568571, "end": 1748240568602, "duration": 31, "pid": 29652, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748240568604, "end": 1748240568606, "duration": 2, "pid": 29652, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748240568609, "end": 1748240568610, "duration": 1, "pid": 29652, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748240568611, "end": 1748240568615, "duration": 4, "pid": 29652, "index": 50}, {"name": "Load extend/helper.js", "start": 1748240568626, "end": 1748240568677, "duration": 51, "pid": 29652, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748240568627, "end": 1748240568658, "duration": 31, "pid": 29652, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748240568666, "end": 1748240568667, "duration": 1, "pid": 29652, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748240568668, "end": 1748240568668, "duration": 0, "pid": 29652, "index": 54}, {"name": "Load app.js", "start": 1748240568677, "end": 1748240568786, "duration": 109, "pid": 29652, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748240568678, "end": 1748240568678, "duration": 0, "pid": 29652, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748240568679, "end": 1748240568682, "duration": 3, "pid": 29652, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748240568684, "end": 1748240568704, "duration": 20, "pid": 29652, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748240568705, "end": 1748240568721, "duration": 16, "pid": 29652, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748240568722, "end": 1748240568737, "duration": 15, "pid": 29652, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748240568738, "end": 1748240568739, "duration": 1, "pid": 29652, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748240568739, "end": 1748240568743, "duration": 4, "pid": 29652, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748240568743, "end": 1748240568744, "duration": 1, "pid": 29652, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748240568744, "end": 1748240568745, "duration": 1, "pid": 29652, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748240568745, "end": 1748240568746, "duration": 1, "pid": 29652, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748240568748, "end": 1748240568749, "duration": 1, "pid": 29652, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748240568749, "end": 1748240568750, "duration": 1, "pid": 29652, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748240568750, "end": 1748240568751, "duration": 1, "pid": 29652, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748240568752, "end": 1748240568759, "duration": 7, "pid": 29652, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748240568760, "end": 1748240568778, "duration": 18, "pid": 29652, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748240568779, "end": 1748240568785, "duration": 6, "pid": 29652, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748240568802, "end": 1748240570560, "duration": 1758, "pid": 29652, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748240569660, "end": 1748240569753, "duration": 93, "pid": 29652, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748240569688, "end": 1748240570487, "duration": 799, "pid": 29652, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748240569793, "end": 1748240570581, "duration": 788, "pid": 29652, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748240569909, "end": 1748240570562, "duration": 653, "pid": 29652, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748240570041, "end": 1748240570512, "duration": 471, "pid": 29652, "index": 77}, {"name": "Load Service", "start": 1748240570042, "end": 1748240570188, "duration": 146, "pid": 29652, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748240570042, "end": 1748240570188, "duration": 146, "pid": 29652, "index": 79}, {"name": "Load Middleware", "start": 1748240570189, "end": 1748240570368, "duration": 179, "pid": 29652, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748240570189, "end": 1748240570351, "duration": 162, "pid": 29652, "index": 81}, {"name": "Load Controller", "start": 1748240570369, "end": 1748240570413, "duration": 44, "pid": 29652, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748240570369, "end": 1748240570413, "duration": 44, "pid": 29652, "index": 83}, {"name": "Load Router", "start": 1748240570413, "end": 1748240570421, "duration": 8, "pid": 29652, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748240570414, "end": 1748240570415, "duration": 1, "pid": 29652, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748240570416, "end": 1748240570487, "duration": 71, "pid": 29652, "index": 86}]