[{"name": "Process Start", "start": 1748240502854, "end": 1748240505015, "duration": 2161, "pid": 31428, "index": 0}, {"name": "Application Start", "start": 1748240505017, "end": 1748240507750, "duration": 2733, "pid": 31428, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748240505040, "end": 1748240505077, "duration": 37, "pid": 31428, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748240505077, "end": 1748240505124, "duration": 47, "pid": 31428, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748240505079, "end": 1748240505079, "duration": 0, "pid": 31428, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748240505081, "end": 1748240505082, "duration": 1, "pid": 31428, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748240505083, "end": 1748240505084, "duration": 1, "pid": 31428, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748240505084, "end": 1748240505085, "duration": 1, "pid": 31428, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748240505087, "end": 1748240505087, "duration": 0, "pid": 31428, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748240505088, "end": 1748240505089, "duration": 1, "pid": 31428, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748240505090, "end": 1748240505091, "duration": 1, "pid": 31428, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748240505092, "end": 1748240505093, "duration": 1, "pid": 31428, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748240505095, "end": 1748240505096, "duration": 1, "pid": 31428, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748240505097, "end": 1748240505097, "duration": 0, "pid": 31428, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748240505098, "end": 1748240505099, "duration": 1, "pid": 31428, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748240505100, "end": 1748240505100, "duration": 0, "pid": 31428, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748240505101, "end": 1748240505102, "duration": 1, "pid": 31428, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748240505103, "end": 1748240505104, "duration": 1, "pid": 31428, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748240505104, "end": 1748240505105, "duration": 1, "pid": 31428, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748240505106, "end": 1748240505106, "duration": 0, "pid": 31428, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748240505107, "end": 1748240505107, "duration": 0, "pid": 31428, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748240505108, "end": 1748240505108, "duration": 0, "pid": 31428, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748240505109, "end": 1748240505109, "duration": 0, "pid": 31428, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748240505110, "end": 1748240505111, "duration": 1, "pid": 31428, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748240505112, "end": 1748240505112, "duration": 0, "pid": 31428, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748240505114, "end": 1748240505114, "duration": 0, "pid": 31428, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748240505115, "end": 1748240505115, "duration": 0, "pid": 31428, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748240505117, "end": 1748240505117, "duration": 0, "pid": 31428, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748240505120, "end": 1748240505120, "duration": 0, "pid": 31428, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748240505123, "end": 1748240505124, "duration": 1, "pid": 31428, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748240505124, "end": 1748240505124, "duration": 0, "pid": 31428, "index": 30}, {"name": "Load extend/application.js", "start": 1748240505125, "end": 1748240505246, "duration": 121, "pid": 31428, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748240505126, "end": 1748240505127, "duration": 1, "pid": 31428, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748240505128, "end": 1748240505130, "duration": 2, "pid": 31428, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748240505130, "end": 1748240505136, "duration": 6, "pid": 31428, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748240505138, "end": 1748240505146, "duration": 8, "pid": 31428, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748240505147, "end": 1748240505149, "duration": 2, "pid": 31428, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748240505150, "end": 1748240505152, "duration": 2, "pid": 31428, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748240505154, "end": 1748240505231, "duration": 77, "pid": 31428, "index": 38}, {"name": "Load extend/request.js", "start": 1748240505246, "end": 1748240505269, "duration": 23, "pid": 31428, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748240505257, "end": 1748240505259, "duration": 2, "pid": 31428, "index": 40}, {"name": "Load extend/response.js", "start": 1748240505269, "end": 1748240505299, "duration": 30, "pid": 31428, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748240505280, "end": 1748240505286, "duration": 6, "pid": 31428, "index": 42}, {"name": "Load extend/context.js", "start": 1748240505299, "end": 1748240505401, "duration": 102, "pid": 31428, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748240505300, "end": 1748240505330, "duration": 30, "pid": 31428, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748240505331, "end": 1748240505335, "duration": 4, "pid": 31428, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748240505336, "end": 1748240505336, "duration": 0, "pid": 31428, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748240505338, "end": 1748240505376, "duration": 38, "pid": 31428, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748240505379, "end": 1748240505381, "duration": 2, "pid": 31428, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748240505383, "end": 1748240505384, "duration": 1, "pid": 31428, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748240505385, "end": 1748240505389, "duration": 4, "pid": 31428, "index": 50}, {"name": "Load extend/helper.js", "start": 1748240505401, "end": 1748240505482, "duration": 81, "pid": 31428, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748240505403, "end": 1748240505446, "duration": 43, "pid": 31428, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748240505462, "end": 1748240505463, "duration": 1, "pid": 31428, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748240505465, "end": 1748240505466, "duration": 1, "pid": 31428, "index": 54}, {"name": "Load app.js", "start": 1748240505482, "end": 1748240505655, "duration": 173, "pid": 31428, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748240505483, "end": 1748240505484, "duration": 1, "pid": 31428, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748240505485, "end": 1748240505490, "duration": 5, "pid": 31428, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748240505502, "end": 1748240505530, "duration": 28, "pid": 31428, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748240505531, "end": 1748240505555, "duration": 24, "pid": 31428, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748240505556, "end": 1748240505580, "duration": 24, "pid": 31428, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748240505581, "end": 1748240505582, "duration": 1, "pid": 31428, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748240505582, "end": 1748240505585, "duration": 3, "pid": 31428, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748240505585, "end": 1748240505586, "duration": 1, "pid": 31428, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748240505587, "end": 1748240505587, "duration": 0, "pid": 31428, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748240505588, "end": 1748240505588, "duration": 0, "pid": 31428, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748240505591, "end": 1748240505591, "duration": 0, "pid": 31428, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748240505592, "end": 1748240505592, "duration": 0, "pid": 31428, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748240505593, "end": 1748240505594, "duration": 1, "pid": 31428, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748240505595, "end": 1748240505598, "duration": 3, "pid": 31428, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748240505599, "end": 1748240505634, "duration": 35, "pid": 31428, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748240505635, "end": 1748240505650, "duration": 15, "pid": 31428, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748240505682, "end": 1748240507726, "duration": 2044, "pid": 31428, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748240506513, "end": 1748240506616, "duration": 103, "pid": 31428, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748240506538, "end": 1748240507661, "duration": 1123, "pid": 31428, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748240506669, "end": 1748240507749, "duration": 1080, "pid": 31428, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748240506796, "end": 1748240507729, "duration": 933, "pid": 31428, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748240507005, "end": 1748240507686, "duration": 681, "pid": 31428, "index": 77}, {"name": "Load Service", "start": 1748240507006, "end": 1748240507262, "duration": 256, "pid": 31428, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748240507006, "end": 1748240507262, "duration": 256, "pid": 31428, "index": 79}, {"name": "Load Middleware", "start": 1748240507262, "end": 1748240507502, "duration": 240, "pid": 31428, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748240507262, "end": 1748240507475, "duration": 213, "pid": 31428, "index": 81}, {"name": "Load Controller", "start": 1748240507502, "end": 1748240507574, "duration": 72, "pid": 31428, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748240507503, "end": 1748240507574, "duration": 71, "pid": 31428, "index": 83}, {"name": "Load Router", "start": 1748240507574, "end": 1748240507583, "duration": 9, "pid": 31428, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748240507575, "end": 1748240507576, "duration": 1, "pid": 31428, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748240507578, "end": 1748240507661, "duration": 83, "pid": 31428, "index": 86}]