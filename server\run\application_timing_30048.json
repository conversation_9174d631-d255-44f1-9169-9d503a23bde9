[{"name": "Process Start", "start": 1748240343866, "end": 1748240352094, "duration": 8228, "pid": 30048, "index": 0}, {"name": "Application Start", "start": 1748240352099, "end": 1748240370058, "duration": 17959, "pid": 30048, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748240352358, "end": 1748240352663, "duration": 305, "pid": 30048, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748240352664, "end": 1748240352938, "duration": 274, "pid": 30048, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748240352670, "end": 1748240352675, "duration": 5, "pid": 30048, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748240352703, "end": 1748240352707, "duration": 4, "pid": 30048, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748240352713, "end": 1748240352714, "duration": 1, "pid": 30048, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748240352716, "end": 1748240352744, "duration": 28, "pid": 30048, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748240352749, "end": 1748240352751, "duration": 2, "pid": 30048, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748240352753, "end": 1748240352757, "duration": 4, "pid": 30048, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748240352761, "end": 1748240352763, "duration": 2, "pid": 30048, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748240352765, "end": 1748240352766, "duration": 1, "pid": 30048, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748240352768, "end": 1748240352769, "duration": 1, "pid": 30048, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748240352774, "end": 1748240352791, "duration": 17, "pid": 30048, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748240352794, "end": 1748240352796, "duration": 2, "pid": 30048, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748240352798, "end": 1748240352801, "duration": 3, "pid": 30048, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748240352804, "end": 1748240352806, "duration": 2, "pid": 30048, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748240352809, "end": 1748240352834, "duration": 25, "pid": 30048, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748240352836, "end": 1748240352838, "duration": 2, "pid": 30048, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748240352841, "end": 1748240352842, "duration": 1, "pid": 30048, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748240352843, "end": 1748240352864, "duration": 21, "pid": 30048, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748240352867, "end": 1748240352869, "duration": 2, "pid": 30048, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748240352870, "end": 1748240352876, "duration": 6, "pid": 30048, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748240352880, "end": 1748240352897, "duration": 17, "pid": 30048, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748240352899, "end": 1748240352901, "duration": 2, "pid": 30048, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748240352907, "end": 1748240352907, "duration": 0, "pid": 30048, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748240352910, "end": 1748240352911, "duration": 1, "pid": 30048, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748240352915, "end": 1748240352916, "duration": 1, "pid": 30048, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748240352921, "end": 1748240352925, "duration": 4, "pid": 30048, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748240352936, "end": 1748240352937, "duration": 1, "pid": 30048, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748240352937, "end": 1748240352937, "duration": 0, "pid": 30048, "index": 30}, {"name": "Load extend/application.js", "start": 1748240352944, "end": 1748240353395, "duration": 451, "pid": 30048, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748240352946, "end": 1748240352948, "duration": 2, "pid": 30048, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748240352949, "end": 1748240352955, "duration": 6, "pid": 30048, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748240352959, "end": 1748240352980, "duration": 21, "pid": 30048, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748240352991, "end": 1748240353015, "duration": 24, "pid": 30048, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748240353019, "end": 1748240353028, "duration": 9, "pid": 30048, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748240353030, "end": 1748240353052, "duration": 22, "pid": 30048, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748240353061, "end": 1748240353358, "duration": 297, "pid": 30048, "index": 38}, {"name": "Load extend/request.js", "start": 1748240353395, "end": 1748240353452, "duration": 57, "pid": 30048, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748240353419, "end": 1748240353428, "duration": 9, "pid": 30048, "index": 40}, {"name": "Load extend/response.js", "start": 1748240353452, "end": 1748240353514, "duration": 62, "pid": 30048, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748240353483, "end": 1748240353494, "duration": 11, "pid": 30048, "index": 42}, {"name": "Load extend/context.js", "start": 1748240353514, "end": 1748240354235, "duration": 721, "pid": 30048, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748240353516, "end": 1748240353702, "duration": 186, "pid": 30048, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748240353703, "end": 1748240353952, "duration": 249, "pid": 30048, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748240353974, "end": 1748240353976, "duration": 2, "pid": 30048, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748240353981, "end": 1748240354179, "duration": 198, "pid": 30048, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748240354182, "end": 1748240354187, "duration": 5, "pid": 30048, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748240354195, "end": 1748240354198, "duration": 3, "pid": 30048, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748240354201, "end": 1748240354216, "duration": 15, "pid": 30048, "index": 50}, {"name": "Load extend/helper.js", "start": 1748240354235, "end": 1748240354413, "duration": 178, "pid": 30048, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748240354237, "end": 1748240354375, "duration": 138, "pid": 30048, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748240354385, "end": 1748240354387, "duration": 2, "pid": 30048, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748240354391, "end": 1748240354393, "duration": 2, "pid": 30048, "index": 54}, {"name": "Load app.js", "start": 1748240354414, "end": 1748240355033, "duration": 619, "pid": 30048, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748240354415, "end": 1748240354416, "duration": 1, "pid": 30048, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748240354417, "end": 1748240354429, "duration": 12, "pid": 30048, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748240354432, "end": 1748240354504, "duration": 72, "pid": 30048, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748240354507, "end": 1748240354595, "duration": 88, "pid": 30048, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748240354597, "end": 1748240354784, "duration": 187, "pid": 30048, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748240354785, "end": 1748240354792, "duration": 7, "pid": 30048, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748240354794, "end": 1748240354812, "duration": 18, "pid": 30048, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748240354814, "end": 1748240354816, "duration": 2, "pid": 30048, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748240354817, "end": 1748240354818, "duration": 1, "pid": 30048, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748240354820, "end": 1748240354832, "duration": 12, "pid": 30048, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748240354834, "end": 1748240354835, "duration": 1, "pid": 30048, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748240354836, "end": 1748240354837, "duration": 1, "pid": 30048, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748240354841, "end": 1748240354842, "duration": 1, "pid": 30048, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748240354843, "end": 1748240354886, "duration": 43, "pid": 30048, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748240354889, "end": 1748240354965, "duration": 76, "pid": 30048, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748240354966, "end": 1748240355030, "duration": 64, "pid": 30048, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748240355095, "end": 1748240369993, "duration": 14898, "pid": 30048, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748240364653, "end": 1748240365076, "duration": 423, "pid": 30048, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748240364748, "end": 1748240369552, "duration": 4804, "pid": 30048, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748240365256, "end": 1748240370043, "duration": 4787, "pid": 30048, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748240366198, "end": 1748240369984, "duration": 3786, "pid": 30048, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748240366785, "end": 1748240369840, "duration": 3055, "pid": 30048, "index": 77}, {"name": "Load Service", "start": 1748240366787, "end": 1748240367360, "duration": 573, "pid": 30048, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748240366788, "end": 1748240367360, "duration": 572, "pid": 30048, "index": 79}, {"name": "Load Middleware", "start": 1748240367360, "end": 1748240368310, "duration": 950, "pid": 30048, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748240367361, "end": 1748240368271, "duration": 910, "pid": 30048, "index": 81}, {"name": "Load Controller", "start": 1748240368311, "end": 1748240368829, "duration": 518, "pid": 30048, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748240368311, "end": 1748240368829, "duration": 518, "pid": 30048, "index": 83}, {"name": "Load Router", "start": 1748240368829, "end": 1748240368876, "duration": 47, "pid": 30048, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748240368830, "end": 1748240368843, "duration": 13, "pid": 30048, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748240368850, "end": 1748240369551, "duration": 701, "pid": 30048, "index": 86}]