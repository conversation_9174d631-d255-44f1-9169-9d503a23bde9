[{"name": "Process Start", "start": 1748240395250, "end": 1748240400559, "duration": 5309, "pid": 4396, "index": 0}, {"name": "Application Start", "start": 1748240400569, "end": 1748240404184, "duration": 3615, "pid": 4396, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748240400628, "end": 1748240400788, "duration": 160, "pid": 4396, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748240400788, "end": 1748240400938, "duration": 150, "pid": 4396, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748240400791, "end": 1748240400792, "duration": 1, "pid": 4396, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748240400799, "end": 1748240400802, "duration": 3, "pid": 4396, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748240400806, "end": 1748240400807, "duration": 1, "pid": 4396, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748240400809, "end": 1748240400810, "duration": 1, "pid": 4396, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748240400817, "end": 1748240400819, "duration": 2, "pid": 4396, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748240400823, "end": 1748240400824, "duration": 1, "pid": 4396, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748240400825, "end": 1748240400837, "duration": 12, "pid": 4396, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748240400839, "end": 1748240400840, "duration": 1, "pid": 4396, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748240400841, "end": 1748240400842, "duration": 1, "pid": 4396, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748240400844, "end": 1748240400848, "duration": 4, "pid": 4396, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748240400850, "end": 1748240400855, "duration": 5, "pid": 4396, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748240400857, "end": 1748240400858, "duration": 1, "pid": 4396, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748240400860, "end": 1748240400865, "duration": 5, "pid": 4396, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748240400868, "end": 1748240400871, "duration": 3, "pid": 4396, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748240400874, "end": 1748240400875, "duration": 1, "pid": 4396, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748240400880, "end": 1748240400882, "duration": 2, "pid": 4396, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748240400885, "end": 1748240400886, "duration": 1, "pid": 4396, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748240400887, "end": 1748240400888, "duration": 1, "pid": 4396, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748240400889, "end": 1748240400890, "duration": 1, "pid": 4396, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748240400891, "end": 1748240400893, "duration": 2, "pid": 4396, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748240400894, "end": 1748240400895, "duration": 1, "pid": 4396, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748240400904, "end": 1748240400904, "duration": 0, "pid": 4396, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748240400907, "end": 1748240400909, "duration": 2, "pid": 4396, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748240400912, "end": 1748240400913, "duration": 1, "pid": 4396, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748240400921, "end": 1748240400922, "duration": 1, "pid": 4396, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748240400931, "end": 1748240400934, "duration": 3, "pid": 4396, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748240400937, "end": 1748240400937, "duration": 0, "pid": 4396, "index": 30}, {"name": "Load extend/application.js", "start": 1748240400942, "end": 1748240401229, "duration": 287, "pid": 4396, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748240400943, "end": 1748240400953, "duration": 10, "pid": 4396, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748240400954, "end": 1748240400959, "duration": 5, "pid": 4396, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748240400968, "end": 1748240400987, "duration": 19, "pid": 4396, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748240400990, "end": 1748240401022, "duration": 32, "pid": 4396, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748240401025, "end": 1748240401031, "duration": 6, "pid": 4396, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748240401032, "end": 1748240401036, "duration": 4, "pid": 4396, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748240401038, "end": 1748240401216, "duration": 178, "pid": 4396, "index": 38}, {"name": "Load extend/request.js", "start": 1748240401230, "end": 1748240401408, "duration": 178, "pid": 4396, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748240401380, "end": 1748240401384, "duration": 4, "pid": 4396, "index": 40}, {"name": "Load extend/response.js", "start": 1748240401408, "end": 1748240401440, "duration": 32, "pid": 4396, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748240401424, "end": 1748240401430, "duration": 6, "pid": 4396, "index": 42}, {"name": "Load extend/context.js", "start": 1748240401440, "end": 1748240401611, "duration": 171, "pid": 4396, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748240401442, "end": 1748240401485, "duration": 43, "pid": 4396, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748240401486, "end": 1748240401489, "duration": 3, "pid": 4396, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748240401491, "end": 1748240401491, "duration": 0, "pid": 4396, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748240401494, "end": 1748240401571, "duration": 77, "pid": 4396, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748240401574, "end": 1748240401577, "duration": 3, "pid": 4396, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748240401583, "end": 1748240401584, "duration": 1, "pid": 4396, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748240401586, "end": 1748240401591, "duration": 5, "pid": 4396, "index": 50}, {"name": "Load extend/helper.js", "start": 1748240401611, "end": 1748240401711, "duration": 100, "pid": 4396, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748240401616, "end": 1748240401681, "duration": 65, "pid": 4396, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748240401692, "end": 1748240401693, "duration": 1, "pid": 4396, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748240401694, "end": 1748240401696, "duration": 2, "pid": 4396, "index": 54}, {"name": "Load app.js", "start": 1748240401711, "end": 1748240401909, "duration": 198, "pid": 4396, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748240401713, "end": 1748240401729, "duration": 16, "pid": 4396, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748240401730, "end": 1748240401739, "duration": 9, "pid": 4396, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748240401744, "end": 1748240401774, "duration": 30, "pid": 4396, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748240401774, "end": 1748240401801, "duration": 27, "pid": 4396, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748240401802, "end": 1748240401823, "duration": 21, "pid": 4396, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748240401824, "end": 1748240401825, "duration": 1, "pid": 4396, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748240401826, "end": 1748240401855, "duration": 29, "pid": 4396, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748240401856, "end": 1748240401857, "duration": 1, "pid": 4396, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748240401858, "end": 1748240401858, "duration": 0, "pid": 4396, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748240401859, "end": 1748240401859, "duration": 0, "pid": 4396, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748240401861, "end": 1748240401862, "duration": 1, "pid": 4396, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748240401863, "end": 1748240401864, "duration": 1, "pid": 4396, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748240401865, "end": 1748240401866, "duration": 1, "pid": 4396, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748240401868, "end": 1748240401874, "duration": 6, "pid": 4396, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748240401875, "end": 1748240401899, "duration": 24, "pid": 4396, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748240401899, "end": 1748240401907, "duration": 8, "pid": 4396, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748240401928, "end": 1748240404141, "duration": 2213, "pid": 4396, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748240402858, "end": 1748240402948, "duration": 90, "pid": 4396, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748240402888, "end": 1748240404014, "duration": 1126, "pid": 4396, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748240402978, "end": 1748240404183, "duration": 1205, "pid": 4396, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748240403080, "end": 1748240404144, "duration": 1064, "pid": 4396, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748240403185, "end": 1748240404070, "duration": 885, "pid": 4396, "index": 77}, {"name": "Load Service", "start": 1748240403185, "end": 1748240403346, "duration": 161, "pid": 4396, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748240403185, "end": 1748240403346, "duration": 161, "pid": 4396, "index": 79}, {"name": "Load Middleware", "start": 1748240403346, "end": 1748240403574, "duration": 228, "pid": 4396, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748240403346, "end": 1748240403532, "duration": 186, "pid": 4396, "index": 81}, {"name": "Load Controller", "start": 1748240403574, "end": 1748240403647, "duration": 73, "pid": 4396, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748240403574, "end": 1748240403647, "duration": 73, "pid": 4396, "index": 83}, {"name": "Load Router", "start": 1748240403647, "end": 1748240403655, "duration": 8, "pid": 4396, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748240403648, "end": 1748240403649, "duration": 1, "pid": 4396, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748240403650, "end": 1748240404014, "duration": 364, "pid": 4396, "index": 86}]